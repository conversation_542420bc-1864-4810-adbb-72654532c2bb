#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查剪映项目状态和结构
"""

import json
import os
from pathlib import Path

def check_project_structure():
    """检查项目结构和文件状态"""
    
    print("🔍 检查剪映项目结构...")
    print("=" * 60)
    
    # 检查主要文件
    files_to_check = [
        "draft_info.json",
        "draft_meta_info.json", 
        "draft_settings",
        "subdraft/68DE5F2F-1C2E-4118-9DF9-8521B1BE8A44/draft_content.json",
        "subdraft/68DE5F2F-1C2E-4118-9DF9-8521B1BE8A44/sub_draft_config.json"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({size} bytes)")
        else:
            print(f"❌ {file_path} (不存在)")
    
    print("\n" + "=" * 60)
    
    # 检查子项目配置
    sub_config_path = "subdraft/68DE5F2F-1C2E-4118-9DF9-8521B1BE8A44/sub_draft_config.json"
    if os.path.exists(sub_config_path):
        print("📋 子项目配置信息:")
        try:
            with open(sub_config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"   - 名称: {config.get('name', '未知')}")
            print(f"   - 类型: {config.get('type', '未知')}")
            print(f"   - 时长: {config.get('rough_cut_duration', 0) / 1000000:.1f}秒")
            print(f"   - 来源: {config.get('source', '未知')}")
        except Exception as e:
            print(f"   ❌ 读取配置失败: {e}")
    
    # 检查我们添加的水印配置
    draft_content_path = "subdraft/68DE5F2F-1C2E-4118-9DF9-8521B1BE8A44/draft_content.json"
    if os.path.exists(draft_content_path):
        print("\n📝 水印配置检查:")
        try:
            with open(draft_content_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检查文字素材
            texts = data.get('materials', {}).get('texts', [])
            watermark_found = False
            for text in texts:
                if text.get('id') == 'WATERMARK_TEXT_001':
                    watermark_found = True
                    print(f"   ✅ 水印文字: {text.get('content')}")
                    break
            
            if not watermark_found:
                print("   ❌ 未找到水印文字素材")
            
            # 检查关键帧
            keyframes = data.get('keyframes', {}).get('texts', [])
            keyframe_found = False
            for kf in keyframes:
                if kf.get('id') == 'WATERMARK_KEYFRAME_001':
                    keyframe_found = True
                    print(f"   ✅ 动画关键帧: {len(kf.get('keyframe_list', []))}个")
                    break
            
            if not keyframe_found:
                print("   ❌ 未找到动画关键帧")
            
            # 检查轨道
            tracks = data.get('tracks', [])
            track_found = False
            for track in tracks:
                if track.get('id') == 'WATERMARK_TEXT_TRACK_001':
                    track_found = True
                    print(f"   ✅ 文字轨道: {track.get('name')}")
                    break
            
            if not track_found:
                print("   ❌ 未找到文字轨道")
                
        except Exception as e:
            print(f"   ❌ 检查失败: {e}")
    
    print("\n" + "=" * 60)
    print("💡 问题分析:")
    print("   1. 剪映可能使用加密的主项目文件")
    print("   2. 子项目文件的修改可能不会直接反映到主界面")
    print("   3. 需要通过剪映界面手动添加水印")
    
    print("\n🎯 建议解决方案:")
    print("   1. 使用剪映界面手动添加文字水印")
    print("   2. 按照操作指南设置浮动动画")
    print("   3. 参考 '剪映界面添加浮动水印指南.md' 文件")

def generate_keyframe_coordinates():
    """生成关键帧坐标供手动输入"""
    
    print("\n📐 浮动水印关键帧坐标:")
    print("=" * 40)
    
    keyframes = [
        {"time": "0秒", "position": "(100, 100)", "description": "左上角"},
        {"time": "30.5秒", "position": "(1720, 100)", "description": "右上角"},
        {"time": "61秒", "position": "(1720, 880)", "description": "右下角"},
        {"time": "91.5秒", "position": "(100, 880)", "description": "左下角"},
        {"time": "122秒", "position": "(100, 100)", "description": "回到左上角"}
    ]
    
    for i, kf in enumerate(keyframes, 1):
        print(f"关键帧{i}: {kf['time']} -> X={kf['position'].split(',')[0].strip('(')}, Y={kf['position'].split(',')[1].strip(')')} ({kf['description']})")
    
    print("\n📝 文字设置:")
    print("   - 内容: kate人不错")
    print("   - 字体: PingFang SC")
    print("   - 大小: 48px")
    print("   - 颜色: 白色 (#FFFFFF)")
    print("   - 透明度: 80%")
    print("   - 阴影: 黑色，模糊6，距离8，角度315°")

if __name__ == "__main__":
    check_project_structure()
    generate_keyframe_coordinates()
    
    print("\n" + "=" * 60)
    print("📖 详细操作步骤请查看: 剪映界面添加浮动水印指南.md")
    print("=" * 60)
