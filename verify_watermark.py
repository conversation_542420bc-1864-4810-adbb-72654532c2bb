#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证剪映项目中的水印配置
"""

import json
import os

def verify_watermark_config():
    """验证水印配置是否正确添加"""
    
    # 文件路径
    draft_file = "subdraft/68DE5F2F-1C2E-4118-9DF9-8521B1BE8A44/draft_content.json"
    
    if not os.path.exists(draft_file):
        print(f"❌ 文件不存在: {draft_file}")
        return False
    
    try:
        with open(draft_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("✅ JSON文件加载成功")
        
        # 检查文字素材
        texts = data.get('materials', {}).get('texts', [])
        watermark_text = None
        for text in texts:
            if text.get('id') == 'WATERMARK_TEXT_001':
                watermark_text = text
                break
        
        if watermark_text:
            print(f"✅ 找到水印文字素材: {watermark_text.get('content')}")
            print(f"   - 字体: {watermark_text.get('font_name')}")
            print(f"   - 大小: {watermark_text.get('font_size')}")
            print(f"   - 颜色: {watermark_text.get('text_color')}")
        else:
            print("❌ 未找到水印文字素材")
            return False
        
        # 检查关键帧动画
        text_keyframes = data.get('keyframes', {}).get('texts', [])
        watermark_keyframe = None
        for keyframe in text_keyframes:
            if keyframe.get('id') == 'WATERMARK_KEYFRAME_001':
                watermark_keyframe = keyframe
                break
        
        if watermark_keyframe:
            print("✅ 找到水印动画关键帧")
            keyframe_list = watermark_keyframe.get('keyframe_list', [])
            print(f"   - 关键帧数量: {len(keyframe_list)}")
            for i, kf in enumerate(keyframe_list):
                time_sec = kf.get('time', 0) / 1000000  # 转换为秒
                pos = kf.get('values', [0, 0])
                print(f"   - 第{i+1}个关键帧: {time_sec:.1f}秒 位置({pos[0]:.0f}, {pos[1]:.0f})")
        else:
            print("❌ 未找到水印动画关键帧")
            return False
        
        # 检查文字轨道
        tracks = data.get('tracks', [])
        watermark_track = None
        for track in tracks:
            if track.get('id') == 'WATERMARK_TEXT_TRACK_001':
                watermark_track = track
                break
        
        if watermark_track:
            print("✅ 找到水印文字轨道")
            print(f"   - 轨道类型: {watermark_track.get('type')}")
            print(f"   - 轨道名称: {watermark_track.get('name')}")
            segments = watermark_track.get('segments', [])
            if segments:
                segment = segments[0]
                duration_sec = segment.get('target_timerange', {}).get('duration', 0) / 1000000
                alpha = segment.get('clip', {}).get('alpha', 1.0)
                print(f"   - 持续时间: {duration_sec:.1f}秒")
                print(f"   - 透明度: {alpha}")
        else:
            print("❌ 未找到水印文字轨道")
            return False
        
        print("\n🎉 水印配置验证成功！")
        print("\n📝 水印效果说明:")
        print("   - 文字内容: 'kate人不错'")
        print("   - 浮动路径: 左上角 → 右上角 → 右下角 → 左下角 → 左上角")
        print("   - 动画时长: 约122秒（整个视频长度）")
        print("   - 字体: PingFang SC, 48px")
        print("   - 颜色: 白色，透明度80%")
        print("   - 阴影: 黑色阴影效果")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False

if __name__ == "__main__":
    print("🔍 开始验证剪映项目水印配置...")
    print("=" * 50)
    verify_watermark_config()
